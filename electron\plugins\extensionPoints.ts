/**
 * Extension Points
 * Defines all available hooks for plugins to extend functionality
 */

// File Processing Extension Point (existing, to be refactored)
export interface FileProcessorExtension {
  supportedTypes: string[]
  supportedExtensions: string[]
  canProcess(filePath: string, fileType: string): boolean
  process(filePath: string): Promise<ProcessedFileContent>
}

// Chat Enhancement Extension Point
export interface ChatExtension {
  // Message processing hooks
  beforeMessageSend?(message: any): Promise<any>
  afterMessageReceived?(message: any): Promise<any>
  
  // Context enhancement
  provideContextData?(): Promise<any>
  enhancePrompt?(prompt: string, context: any): Promise<string>
  
  // Response processing
  processResponse?(response: any): Promise<any>
}

// Model Integration Extension Point
export interface ModelExtension {
  // Model discovery and registration
  discoverModels?(): Promise<any[]>
  
  // Request/response interception
  interceptModelRequest?(request: any): Promise<any>
  processModelResponse?(response: any): Promise<any>
  
  // Custom model providers
  provideCustomModel?(): Promise<any>
}

// UI Extension Point
export interface UIExtension {
  // Component injection
  provideComponent?(location: UILocation, props?: any): React.ComponentType<any>
  
  // Menu and toolbar extensions
  provideMenuItem?(location: MenuLocation): MenuItem
  provideToolbarItem?(location: ToolbarLocation): ToolbarItem
  
  // Settings panels
  provideSettingsPanel?(): SettingsPanel
}

// API Extension Point
export interface APIExtension {
  // Custom API endpoints
  registerEndpoints(apiRegistry: APIRegistry): void
  
  // Middleware
  provideMiddleware?(): Middleware[]
}

// Intelligence Processing Extension Point
export interface IntelligenceExtension {
  // Entity extraction
  extractEntities?(content: string): Promise<any[]>
  
  // Topic analysis
  analyzeTopics?(content: string): Promise<any[]>
  
  // Content enhancement
  enhanceContent?(content: string): Promise<string>
}

export enum UILocation {
  SIDEBAR_TOP = 'sidebar_top',
  SIDEBAR_BOTTOM = 'sidebar_bottom',
  CHAT_INPUT_TOOLBAR = 'chat_input_toolbar',
  MESSAGE_ACTIONS = 'message_actions',
  SETTINGS_TABS = 'settings_tabs'
}

export enum MenuLocation {
  MAIN_MENU = 'main_menu',
  CONTEXT_MENU = 'context_menu',
  FILE_MENU = 'file_menu'
}