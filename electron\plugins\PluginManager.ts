/**
 * Universal Plugin Manager
 * Handles all plugin types and capabilities
 */

import * as fs from 'fs'
import * as path from 'path'
import { BasePlugin, PluginManifest, PluginRegistry, PluginState, PluginCapability } from './types'
import { APIRegistry } from '../api/APIRegistry'

export class UniversalPluginManager {
  private registry: PluginRegistry = {
    plugins: new Map(),
    capabilityMap: new Map(),
    configs: new Map(),
    states: new Map()
  }
  
  private pluginDirectories: string[] = []
  private apiRegistry: APIRegistry
  
  constructor(apiRegistry: APIRegistry) {
    this.apiRegistry = apiRegistry
    this.initializeCapabilityMap()
  }
  
  // Initialize capability map
  private initializeCapabilityMap(): void {
    Object.values(PluginCapability).forEach(capability => {
      this.registry.capabilityMap.set(capability, [])
    })
  }
  
  // Add plugin directory for discovery
  addPluginDirectory(directory: string): void {
    if (!this.pluginDirectories.includes(directory)) {
      this.pluginDirectories.push(directory)
    }
  }
  
  // Discover plugins in all registered directories
  async discoverPlugins(): Promise<PluginManifest[]> {
    const manifests: PluginManifest[] = []
    
    for (const directory of this.pluginDirectories) {
      try {
        const dirManifests = await this.discoverPluginsInDirectory(directory)
        manifests.push(...dirManifests)
      } catch (error) {
        console.error(`Error discovering plugins in ${directory}:`, error)
      }
    }
    
    return manifests
  }
  
  // Discover plugins in a specific directory
  private async discoverPluginsInDirectory(directory: string): Promise<PluginManifest[]> {
    const manifests: PluginManifest[] = []
    
    if (!fs.existsSync(directory)) {
      return manifests
    }
    
    const entries = await fs.promises.readdir(directory, { withFileTypes: true })
    
    for (const entry of entries) {
      if (entry.isDirectory()) {
        const manifestPath = path.join(directory, entry.name, 'manifest.json')
        
        if (fs.existsSync(manifestPath)) {
          try {
            const manifestContent = await fs.promises.readFile(manifestPath, 'utf8')
            const manifest = JSON.parse(manifestContent) as PluginManifest
            
            if (this.validateManifest(manifest)) {
              manifests.push(manifest)
            }
          } catch (error) {
            console.error(`Error loading manifest for ${entry.name}:`, error)
          }
        }
      }
    }
    
    return manifests
  }
  
  // Load plugin from manifest
  async loadPlugin(manifest: PluginManifest): Promise<void> {
    try {
      this.registry.states.set(manifest.id, PluginState.LOADING)
      
      // Resolve plugin path
      const pluginPath = this.resolvePluginPath(manifest)
      
      // Load plugin module
      const pluginModule = require(pluginPath)
      const plugin = new pluginModule.default() as BasePlugin
      
      // Validate plugin
      if (!this.validatePlugin(plugin, manifest)) {
        throw new Error(`Plugin validation failed for ${manifest.id}`)
      }
      
      // Initialize plugin
      await plugin.initialize()
      
      // Register plugin
      await this.registerPlugin(plugin)
      
      this.registry.states.set(manifest.id, PluginState.ACTIVE)
      console.log(`Plugin loaded successfully: ${plugin.name} (${plugin.id}) v${plugin.version}`)
      
    } catch (error) {
      this.registry.states.set(manifest.id, PluginState.ERROR)
      console.error(`Error loading plugin ${manifest.id}:`, error)
      throw error
    }
  }
  
  // Register a plugin
  async registerPlugin(plugin: BasePlugin): Promise<void> {
    // Store plugin
    this.registry.plugins.set(plugin.id, plugin)
    
    // Register by capabilities
    const capabilities = plugin.getCapabilities()
    for (const capability of capabilities) {
      const plugins = this.registry.capabilityMap.get(capability) || []
      plugins.push(plugin)
      this.registry.capabilityMap.set(capability, plugins)
    }
    
    // Initialize plugin config
    this.registry.configs.set(plugin.id, plugin.getDefaultConfig())
    
    // Register API endpoints if plugin supports it
    if (capabilities.includes(PluginCapability.API_EXTENSION)) {
      const apiExtension = plugin as any
      if (apiExtension.registerEndpoints) {
        apiExtension.registerEndpoints(this.apiRegistry)
      }
    }
  }
  
  // Get plugins by capability
  getPluginsByCapability(capability: PluginCapability): BasePlugin[] {
    return this.registry.capabilityMap.get(capability) || []
  }
  
  // Get plugin by ID
  getPlugin(pluginId: string): BasePlugin | undefined {
    return this.registry.plugins.get(pluginId)
  }
  
  // Enable/disable plugin
  setPluginEnabled(pluginId: string, enabled: boolean): void {
    const currentState = this.registry.states.get(pluginId)
    
    if (enabled && currentState === PluginState.DISABLED) {
      this.registry.states.set(pluginId, PluginState.ACTIVE)
    } else if (!enabled && currentState === PluginState.ACTIVE) {
      this.registry.states.set(pluginId, PluginState.DISABLED)
    }
  }
  
  // Get plugin state
  getPluginState(pluginId: string): PluginState {
    return this.registry.states.get(pluginId) || PluginState.UNLOADED
  }
  
  // Get all plugins info
  getAllPluginsInfo(): Array<{id: string, name: string, version: string, state: PluginState, capabilities: PluginCapability[]}> {
    const info: Array<{id: string, name: string, version: string, state: PluginState, capabilities: PluginCapability[]}> = []
    
    for (const [id, plugin] of this.registry.plugins.entries()) {
      info.push({
        id,
        name: plugin.name,
        version: plugin.version,
        state: this.getPluginState(id),
        capabilities: plugin.getCapabilities()
      })
    }
    
    return info
  }
  
  // Cleanup all plugins
  async cleanup(): Promise<void> {
    for (const plugin of this.registry.plugins.values()) {
      try {
        if (plugin.cleanup) {
          await plugin.cleanup()
        }
      } catch (error) {
        console.error(`Error cleaning up plugin ${plugin.id}:`, error)
      }
    }
    
    this.registry.plugins.clear()
    this.registry.capabilityMap.clear()
    this.registry.configs.clear()
    this.registry.states.clear()
  }
  
  // Validate plugin manifest
  private validateManifest(manifest: any): boolean {
    const required = ['id', 'name', 'version', 'main', 'capabilities', 'apiVersion']
    return required.every(field => manifest[field] !== undefined)
  }
  
  // Validate plugin instance
  private validatePlugin(plugin: any, manifest: PluginManifest): boolean {
    return (
      plugin.id === manifest.id &&
      plugin.name === manifest.name &&
      plugin.version === manifest.version &&
      typeof plugin.initialize === 'function' &&
      typeof plugin.getCapabilities === 'function' &&
      typeof plugin.getDefaultConfig === 'function'
    )
  }
  
  // Resolve plugin path from manifest
  private resolvePluginPath(manifest: PluginManifest): string {
    // Implementation depends on your plugin directory structure
    // This is a simplified version
    for (const directory of this.pluginDirectories) {
      const pluginPath = path.join(directory, manifest.id, manifest.main)
      if (fs.existsSync(pluginPath)) {
        return pluginPath
      }
    }
    throw new Error(`Plugin main file not found: ${manifest.main}`)
  }
}