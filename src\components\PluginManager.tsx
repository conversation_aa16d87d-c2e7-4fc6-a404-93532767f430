/**
 * Plugin Management UI Component
 */

import React, { useState, useEffect } from 'react'
import { Settings, Power, Download, Trash2 } from './Icons'

interface PluginInfo {
  id: string
  name: string
  version: string
  state: string
  capabilities: string[]
  description?: string
}

export const PluginManager: React.FC = () => {
  const [plugins, setPlugins] = useState<PluginInfo[]>([])
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    loadPlugins()
  }, [])
  
  const loadPlugins = async () => {
    try {
      if (window.electronAPI?.plugins) {
        const pluginList = await window.electronAPI.plugins.getAll()
        setPlugins(pluginList)
      }
    } catch (error) {
      console.error('Error loading plugins:', error)
    } finally {
      setLoading(false)
    }
  }
  
  const togglePlugin = async (pluginId: string, enabled: boolean) => {
    try {
      if (window.electronAPI?.plugins) {
        await window.electronAPI.plugins.enable(pluginId, enabled)
        await loadPlugins() // Refresh list
      }
    } catch (error) {
      console.error('Error toggling plugin:', error)
    }
  }
  
  const discoverPlugins = async () => {
    try {
      if (window.electronAPI?.plugins) {
        await window.electronAPI.plugins.discover()
        await loadPlugins() // Refresh list
      }
    } catch (error) {
      console.error('Error discovering plugins:', error)
    }
  }
  
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }
  
  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Plugin Manager</h2>
        <button
          onClick={discoverPlugins}
          className="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/80 transition-colors"
        >
          <Download className="w-4 h-4" />
          Discover Plugins
        </button>
      </div>
      
      <div className="grid gap-4">
        {plugins.map(plugin => (
          <div key={plugin.id} className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between mb-2">
              <div>
                <h3 className="text-lg font-semibold text-white">{plugin.name}</h3>
                <p className="text-sm text-gray-400">v{plugin.version} • {plugin.id}</p>
              </div>
              
              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  plugin.state === 'active' ? 'bg-green-900 text-green-300' :
                  plugin.state === 'disabled' ? 'bg-gray-900 text-gray-300' :
                  plugin.state === 'error' ? 'bg-red-900 text-red-300' :
                  'bg-yellow-900 text-yellow-300'
                }`}>
                  {plugin.state}
                </span>
                
                <button
                  onClick={() => togglePlugin(plugin.id, plugin.state !== 'active')}
                  className={`p-2 rounded-lg transition-colors ${
                    plugin.state === 'active' 
                      ? 'bg-red-600 hover:bg-red-700 text-white'
                      : 'bg-green-600 hover:bg-green-700 text-white'
                  }`}
                >
                  <Power className="w-4 h-4" />
                </button>
              </div>
            </div>
            
            {plugin.description && (
              <p className="text-gray-300 text-sm mb-3">{plugin.description}</p>
            )}
            
            <div className="flex flex-wrap gap-2">
              {plugin.capabilities.map(capability => (
                <span
                  key={capability}
                  className="px-2 py-1 bg-blue-900 text-blue-300 rounded text-xs font-medium"
                >
                  {capability.replace('_', ' ')}
                </span>
              ))}
            </div>
          </div>
        ))}
      </div>
      
      {plugins.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-400 mb-4">No plugins found</p>
          <button
            onClick={discoverPlugins}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/80 transition-colors"
          >
            Discover Plugins
          </button>
        </div>
      )}
    </div>
  )
}