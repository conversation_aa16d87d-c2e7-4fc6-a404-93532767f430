/**
 * API Registry
 * Central registry for all API endpoints with plugin support
 */

import { ipcMain } from 'electron'

export interface APIEndpoint {
  handler: Function
  validator?: Function
  middleware?: Function[]
  description?: string
}

export interface APICategory {
  name: string
  endpoints: Map<string, APIEndpoint>
  middleware?: Function[]
}

export class APIRegistry {
  private categories: Map<string, APICategory> = new Map()
  private globalMiddleware: Function[] = []
  
  // Add global middleware
  addGlobalMiddleware(middleware: Function): void {
    this.globalMiddleware.push(middleware)
  }
  
  // Register a new API category
  registerCategory(name: string, middleware?: Function[]): void {
    if (!this.categories.has(name)) {
      this.categories.set(name, {
        name,
        endpoints: new Map(),
        middleware: middleware || []
      })
    }
  }
  
  // Register an endpoint in a category
  registerEndpoint(
    category: string,
    name: string,
    handler: Function,
    options?: {
      validator?: Function
      middleware?: Function[]
      description?: string
    }
  ): void {
    if (!this.categories.has(category)) {
      this.registerCategory(category)
    }
    
    const categoryObj = this.categories.get(category)!
    categoryObj.endpoints.set(name, {
      handler,
      validator: options?.validator,
      middleware: options?.middleware || [],
      description: options?.description
    })
  }
  
  // Initialize all API endpoints with Electron IPC
  initialize(): void {
    for (const [categoryName, category] of this.categories.entries()) {
      for (const [endpointName, endpoint] of category.endpoints.entries()) {
        const channelName = `${categoryName}:${endpointName}`
        
        ipcMain.handle(channelName, async (event, ...args) => {
          try {
            // Security validation
            if (!this.validateSender(event.sender)) {
              throw new Error('Unauthorized sender')
            }
            
            // Run global middleware
            for (const middleware of this.globalMiddleware) {
              await middleware(event, ...args)
            }
            
            // Run category middleware
            if (category.middleware) {
              for (const middleware of category.middleware) {
                await middleware(event, ...args)
              }
            }
            
            // Run endpoint middleware
            if (endpoint.middleware) {
              for (const middleware of endpoint.middleware) {
                await middleware(event, ...args)
              }
            }
            
            // Validate input
            if (endpoint.validator) {
              endpoint.validator(...args)
            }
            
            // Call the handler
            return await endpoint.handler(...args)
            
          } catch (error) {
            console.error(`API Error in ${channelName}:`, error)
            throw error
          }
        })
      }
    }
  }
  
  // Get all registered endpoints
  getAllEndpoints(): Array<{category: string, name: string, description?: string}> {
    const endpoints: Array<{category: string, name: string, description?: string}> = []
    
    for (const [categoryName, category] of this.categories.entries()) {
      for (const [endpointName, endpoint] of category.endpoints.entries()) {
        endpoints.push({
          category: categoryName,
          name: endpointName,
          description: endpoint.description
        })
      }
    }
    
    return endpoints
  }
  
  // Validate sender (security check)
  private validateSender(sender: any): boolean {
    // Implement your security validation logic here
    // For now, we'll allow all senders from the main window
    return true
  }
}