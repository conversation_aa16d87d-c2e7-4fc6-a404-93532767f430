/**
 * Custom Toolbar Plugin
 * Example of UI extension capabilities
 */

import { BasePlugin, PluginCapability } from '../../types'
import { UIExtension, UILocation, ToolbarItem } from '../../extensionPoints'

export default class CustomToolbarPlugin implements BasePlugin, UIExtension {
  id = 'custom-toolbar-enhancer'
  name = 'Custom Toolbar Enhancer'
  version = '1.0.0'
  description = 'Adds custom toolbar items to the chat interface'
  author = 'ChatLo Community'
  
  async initialize(): Promise<void> {
    console.log('Custom Toolbar Plugin initialized')
  }
  
  getCapabilities(): PluginCapability[] {
    return [PluginCapability.UI_EXTENSION]
  }
  
  getDefaultConfig(): Record<string, any> {
    return {
      showQuickActions: true,
      showTemplates: true
    }
  }
  
  // Provide toolbar items
  provideToolbarItem(location: any): ToolbarItem {
    if (location === 'chat_input_toolbar') {
      return {
        id: 'quick-templates',
        label: 'Templates',
        icon: 'template',
        onClick: () => this.showTemplateMenu(),
        tooltip: 'Insert message template'
      }
    }
    
    return null as any
  }
  
  private showTemplateMenu(): void {
    // Implementation would show a template selection menu
    console.log('Showing template menu...')
  }
}