import { app, BrowserWindow, ipcMain, IpcMainInvokeEvent, dialog } from 'electron'
import { autoUpdater } from 'electron-updater'
import path from 'path'
import { isDev } from './utils'
import { DatabaseManager } from './database'
import { FileSystemManager } from './fileSystem'
import { APIRegistry } from './api/APIRegistry'
import { UniversalPluginManager } from './plugins/PluginManager'
import { PluginCapability } from './plugins/types'

class App {
  public mainWindow: BrowserWindow | null = null
  private db: DatabaseManager
  private fileSystem: FileSystemManager
  private apiRegistry: APIRegistry
  private pluginManager: UniversalPluginManager

  constructor() {
    this.db = new DatabaseManager()
    this.fileSystem = new FileSystemManager(this.db)
    
    // Initialize API registry
    this.apiRegistry = new APIRegistry()
    
    // Initialize plugin manager
    this.pluginManager = new UniversalPluginManager(this.apiRegistry)
    
    // Add plugin directories
    this.pluginManager.addPluginDirectory(path.join(__dirname, 'plugins'))
    this.pluginManager.addPluginDirectory(path.join(app.getPath('userData'), 'plugins'))

  private createWindow(): void {
    // Following Electron best practices from official documentation
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false, // Don't show until ready-to-show event
      frame: false, // Frameless window for custom chrome
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'hidden',
      titleBarOverlay: false,
      backgroundColor: '#111827', // Match our app's dark theme (gray-900)
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: true,
      },
    })

    // Use ready-to-show event for graceful window display (Electron best practice)
    this.mainWindow.once('ready-to-show', () => {
      if (this.mainWindow) {
        this.mainWindow.show()

        // Open DevTools in development after window is shown
        if (isDev) {
          this.mainWindow.webContents.openDevTools()
        }
      }
    })

    if (isDev) {
      this.mainWindow.loadURL('http://localhost:5173')
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
    }

    this.mainWindow.on('closed', () => {
      this.mainWindow = null
    })
  }

  private validateSender(frame: any): boolean {
    // Validate the sender is from your app
    if (!frame || !frame.url) return false
    return frame.url.startsWith('file://') ||
           frame.url.startsWith('http://localhost:5173')
  }

  private validateInput(value: any, type: string, maxLength?: number): boolean {
    if (value === null || value === undefined) return false
    if (typeof value !== type) return false
    if (type === 'string' && maxLength && value.length > maxLength) return false
    return true
  }

  private async createContextStructure(contextPath: string, contextName: string): Promise<void> {
    const fs = await import('fs')
    const path = await import('path')

    // Create context directory
    await fs.promises.mkdir(contextPath, { recursive: true })

    // Create subdirectories
    await fs.promises.mkdir(path.join(contextPath, 'documents'), { recursive: true })
    await fs.promises.mkdir(path.join(contextPath, 'images'), { recursive: true })
    await fs.promises.mkdir(path.join(contextPath, 'artifacts'), { recursive: true })
    await fs.promises.mkdir(path.join(contextPath, '.context'), { recursive: true })

    // Create master.md
    const masterContent = `# ${contextName}

Welcome to your intelligent context vault! This is your master document that serves as the central hub for organizing and understanding your project.

## Overview
This context vault helps you organize files, conversations, and AI insights in one place.

## Quick Start
1. **Add files** to the \`documents/\` folder to get started
2. **Start a conversation** using the chat interface with this context selected
3. **Organize insights** and progress notes right here in this document

## How It Works
- 📁 **Documents folder**: Store your project files here
- 🖼️ **Images folder**: Add screenshots, diagrams, and visual assets
- 🎯 **Artifacts folder**: Save important outputs from AI conversations
- 🧠 **AI Memory**: The \`.context/\` folder contains AI memory optimized for Gemma models

## AI Insights
*This section will be automatically updated as you add files and have conversations*

## Project Progress
*Use this space to track your progress and key milestones*

---
*Last updated: ${new Date().toLocaleString()}*
*Files: 0 | Conversations: 0*`

    await fs.promises.writeFile(path.join(contextPath, 'master.md'), masterContent, 'utf8')

    // Create context metadata
    const metadata = {
      id: contextName.toLowerCase().replace(/\s+/g, '-'),
      name: contextName,
      created: new Date().toISOString(),
      description: `Context vault for ${contextName}`,
      contextType: 'getting-started'
    }

    await fs.promises.writeFile(
      path.join(contextPath, '.context', 'metadata.json'),
      JSON.stringify(metadata, null, 2),
      'utf8'
    )

    // Create AI memory file
    const memoryContent = `# AI Memory for ${contextName}

## Context Understanding
This context vault was just created and is ready for your first project.

## Key Concepts
*AI will learn and document key concepts from your files and conversations*

## Relationships
*Connections between files, ideas, and conversations will be tracked here*

## Memory Chunks
*Optimized memory chunks for Gemma models will be stored here*

---
*This file is automatically managed by ChatLo's AI system*`

    await fs.promises.writeFile(path.join(contextPath, '.context', 'ai-memory.md'), memoryContent, 'utf8')

    // Create chat links file
    await fs.promises.writeFile(
      path.join(contextPath, '.context', 'chat-links.json'),
      JSON.stringify({ conversations: [] }, null, 2),
      'utf8'
    )
  }

  private setupIPC(): void {
    // Register core API categories
    this.registerCoreAPIs()
    
    // Initialize API registry (sets up IPC handlers)
    this.apiRegistry.initialize()

    // Database operations with validation
    ipcMain.handle('db:getConversations', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.db.getConversations()
    })

    ipcMain.handle('db:getConversation', (event: IpcMainInvokeEvent, id: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
      return this.db.getConversation(id)
    })

    ipcMain.handle('db:createConversation', (event: IpcMainInvokeEvent, title: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(title, 'string', 200)) throw new Error('Invalid conversation title')
      console.log('Main: Creating conversation with title:', title)
      const id = this.db.createConversation(title)
      console.log('Main: Created conversation:', id)
      return id
    })

    ipcMain.handle('db:updateConversation', (event: IpcMainInvokeEvent, id: string, title: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
      if (!this.validateInput(title, 'string', 200)) throw new Error('Invalid conversation title')
      return this.db.updateConversation(id, title)
    })

    ipcMain.handle('db:deleteConversation', (event: IpcMainInvokeEvent, id: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
      return this.db.deleteConversation(id)
    })

    ipcMain.handle('db:addMessage', (event: IpcMainInvokeEvent, conversationId: string, message: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
      if (!message || typeof message !== 'object') throw new Error('Invalid message object')
      return this.db.addMessage(conversationId, message)
    })

    ipcMain.handle('db:togglePinMessage', (event: IpcMainInvokeEvent, messageId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      this.db.togglePinMessage(messageId)
    })

    // Intelligence-related IPC handlers
    ipcMain.handle('db:updateMessageIntelligence', (event: IpcMainInvokeEvent, messageId: string, entities: string, topics: string, confidence: number) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      if (typeof confidence !== 'number' || confidence < 0 || confidence > 1) throw new Error('Invalid confidence score')
      this.db.updateMessageIntelligence(messageId, entities, topics, confidence)
    })

    ipcMain.handle('db:addPinnedIntelligence', (event: IpcMainInvokeEvent, messageId: string, extractionData: string, vaultAssignment: string, processingMetadata: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      return this.db.addPinnedIntelligence(messageId, extractionData, vaultAssignment, processingMetadata)
    })

    ipcMain.handle('db:getPinnedIntelligence', (event: IpcMainInvokeEvent, messageId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      return this.db.getPinnedIntelligence(messageId)
    })

    ipcMain.handle('db:getAllPinnedIntelligence', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.db.getAllPinnedIntelligence()
    })

    ipcMain.handle('db:getMessages', (event: IpcMainInvokeEvent, conversationId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
      return this.db.getMessages(conversationId)
    })

    ipcMain.handle('db:searchConversations', (event: IpcMainInvokeEvent, searchTerm: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(searchTerm, 'string', 200)) throw new Error('Invalid search term')
      return this.db.searchConversationsAndMessages(searchTerm)
    })

    ipcMain.handle('db:getConversationsWithArtifacts', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.db.getConversationsWithArtifacts()
    })

    // Settings with validation
    ipcMain.handle('settings:get', (event: IpcMainInvokeEvent, key: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(key, 'string', 50)) throw new Error('Invalid settings key')
      return this.db.getSetting(key)
    })

    ipcMain.handle('settings:set', (event: IpcMainInvokeEvent, key: string, value: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(key, 'string', 50)) throw new Error('Invalid settings key')
      this.db.setSetting(key, value)
      console.log('Settings updated:', key, value)
    })

    // File system operations with validation
    ipcMain.handle('files:getChatloFolderPath', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.fileSystem.getChatloFolderPath()
    })

    ipcMain.handle('files:setChatloFolderPath', async (event: IpcMainInvokeEvent, path: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(path, 'string', 500)) throw new Error('Invalid folder path')
      return await this.fileSystem.setChatloFolderPath(path)
    })

    ipcMain.handle('files:getIndexedFiles', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null

      // Check if vault system is configured
      const vaultRootPath = this.db.getSetting('vault-root-path')
      console.log('[ELECTRON] files:getIndexedFiles called, vault root path:', vaultRootPath)

      if (vaultRootPath) {
        console.log('[ELECTRON] Vault system detected, returning empty array for legacy file system')
        return []
      }

      console.log('[ELECTRON] No vault system, using legacy file system')
      return this.fileSystem.getIndexedFiles()
    })

    ipcMain.handle('files:searchFiles', (event: IpcMainInvokeEvent, query: string, limit?: number) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(query, 'string', 200)) throw new Error('Invalid search query')

      // Check if vault system is configured
      const vaultRootPath = this.db.getSetting('vault-root-path')
      console.log('[ELECTRON] files:searchFiles called, vault root path:', vaultRootPath)

      if (vaultRootPath) {
        console.log('[ELECTRON] Vault system detected, returning empty array for legacy file search')
        return []
      }

      console.log('[ELECTRON] No vault system, using legacy file search')
      const searchLimit = limit && limit > 0 && limit <= 50 ? limit : 10 // Max 50 results
      return this.fileSystem.searchFiles(query, searchLimit)
    })

    ipcMain.handle('files:processFileContent', (event: IpcMainInvokeEvent, fileId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(fileId, 'string', 100)) throw new Error('Invalid file ID')
      return this.fileSystem.processFileContent(fileId)
    })

    // Direct file parsing test
    ipcMain.handle('files:testDirectParsing', async (event: IpcMainInvokeEvent, filePath: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')

      try {
        console.log('[ELECTRON] Direct parsing test for:', filePath)

        // Check if file exists
        const fs = await import('fs')
        if (!fs.existsSync(filePath)) {
          return { success: false, error: 'File does not exist' }
        }

        // Get file info
        const stats = fs.statSync(filePath)
        console.log('[ELECTRON] File size:', stats.size, 'bytes')

        // Detect file type
        const fileType = this.fileSystem.getFileType(filePath)
        console.log('[ELECTRON] Detected file type:', fileType)

        // Check if supported
        const isSupported = this.fileSystem.fileProcessor.isFileTypeSupported(fileType)
        console.log('[ELECTRON] Is file type supported:', isSupported)

        if (!isSupported) {
          return { success: false, error: `File type '${fileType}' not supported` }
        }

        // Try to process the file directly
        const result = await this.fileSystem.fileProcessor.processFile(filePath, fileType)
        console.log('[ELECTRON] Direct parsing result:', {
          hasText: !!result.text,
          textLength: result.text?.length || 0,
          hasError: !!result.error,
          error: result.error,
          firstChars: result.text?.substring(0, 200) || 'No text'
        })

        return {
          success: !result.error,
          fileType,
          isSupported,
          textLength: result.text?.length || 0,
          firstChars: result.text?.substring(0, 500) || 'No text',
          error: result.error,
          metadata: result.metadata
        }

      } catch (error) {
        console.error('[ELECTRON] Direct parsing test error:', error)
        return { success: false, error: error instanceof Error ? error.message : String(error) }
      }
    })

    ipcMain.handle('files:indexFile', async (event: IpcMainInvokeEvent, filePath: string, processContent?: boolean) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')

      console.log('[ELECTRON] files:indexFile called with:', filePath, 'processContent:', processContent)

      try {
        const result = await this.fileSystem.indexFile(filePath, processContent || false)
        console.log('[ELECTRON] files:indexFile result:', result)
        return result
      } catch (error) {
        console.error('[ELECTRON] files:indexFile error:', error)
        throw error
      }
    })

    ipcMain.handle('files:indexAllFiles', async (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return await this.fileSystem.indexAllFiles()
    })

    // New vault-aware file indexing for dynamic context vault files
    ipcMain.handle('files:indexVaultFile', async (event: IpcMainInvokeEvent, filePath: string, vaultName: string, relativePath: string, processContent?: boolean) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
      if (!this.validateInput(vaultName, 'string', 100)) throw new Error('Invalid vault name')
      if (!this.validateInput(relativePath, 'string', 500)) throw new Error('Invalid relative path')

      console.log('[ELECTRON] files:indexVaultFile called:', { filePath, vaultName, relativePath, processContent })

      try {
        const fs = await import('fs')
        const path = await import('path')

        // Check if file exists
        if (!fs.existsSync(filePath)) {
          console.warn('[ELECTRON] Vault file does not exist:', filePath)
          return { success: false, error: 'File does not exist' }
        }

        // Check if file is already indexed
        const existingFile = this.db.getVaultFile(vaultName, relativePath)
        if (existingFile) {
          console.log('[ELECTRON] Vault file already indexed:', existingFile.id)

          // If processContent is requested and file doesn't have extracted content, process it
          if (processContent && !existingFile.extracted_content) {
            console.log('[ELECTRON] Existing file needs content processing')
            const fileType = this.fileSystem.getFileType(filePath)

            if (this.fileSystem.fileProcessor.isFileTypeSupported(fileType)) {
              try {
                console.log('[ELECTRON] Processing existing vault file content for:', existingFile.filename)
                const processedContent = await this.fileSystem.fileProcessor.processFile(filePath, fileType)
                console.log('[ELECTRON] Processing result for existing file:', {
                  hasText: !!processedContent.text,
                  textLength: processedContent.text?.length || 0,
                  hasError: !!processedContent.error,
                  error: processedContent.error
                })

                if (processedContent.text && !processedContent.error) {
                  // Update the file record with extracted content
                  this.db.updateFile(existingFile.id, {
                    extracted_content: processedContent.text,
                    metadata: JSON.stringify({
                      ...JSON.parse(existingFile.metadata || '{}'),
                      processed: true,
                      processingMetadata: processedContent.metadata
                    })
                  })
                  console.log('[ELECTRON] Existing vault file content processed successfully:', existingFile.filename, 'Content length:', processedContent.text.length)

                  // Return updated file record
                  const updatedFile = this.db.getFile(existingFile.id)
                  return { success: true, file: updatedFile }
                } else if (processedContent.error) {
                  console.warn('[ELECTRON] Existing vault file processing failed:', existingFile.filename, processedContent.error)
                } else {
                  console.warn('[ELECTRON] Existing vault file processing returned no text and no error:', existingFile.filename)
                }
              } catch (error) {
                console.error('[ELECTRON] Error processing existing vault file content:', existingFile.filename, error)
              }
            } else {
              console.log('[ELECTRON] Existing file type not supported for processing:', fileType)
            }
          }

          return { success: true, file: existingFile }
        }

        const stats = fs.statSync(filePath)
        const filename = path.basename(filePath)
        const fileType = this.fileSystem.getFileType(filename)
        const mimeType = this.fileSystem.getMimeType(filename)
        const contentHash = this.fileSystem.calculateFileHash(filePath)

        // Create a vault file record
        const fileRecord: any = {
          filename,
          filepath: filePath, // Store full path for legacy compatibility
          file_type: fileType,
          file_size: stats.size,
          content_hash: contentHash,
          mime_type: mimeType,
          extracted_content: null,
          metadata: JSON.stringify({
            lastModified: stats.mtime.toISOString(),
            isDirectory: stats.isDirectory(),
            extension: path.extname(filename),
            uploaded_via: 'vault_file'
          }),
          vault_name: vaultName,
          relative_path: relativePath,
          storage_type: 'vault'
        }

        // Add to database and get the ID
        const fileId = this.db.addFile(fileRecord)
        console.log('[ELECTRON] Vault file indexed with ID:', fileId)

        // Process content if requested
        if (processContent) {
          console.log('[ELECTRON] processContent requested for:', filename, 'fileType:', fileType)
          console.log('[ELECTRON] isFileTypeSupported:', this.fileSystem.fileProcessor.isFileTypeSupported(fileType))

          if (this.fileSystem.fileProcessor.isFileTypeSupported(fileType)) {
            try {
              console.log('[ELECTRON] Processing vault file content for:', filename)
              const processedContent = await this.fileSystem.fileProcessor.processFile(filePath, fileType)
              console.log('[ELECTRON] Processing result:', {
                hasText: !!processedContent.text,
                textLength: processedContent.text?.length || 0,
                hasError: !!processedContent.error,
                error: processedContent.error
              })

              if (processedContent.text && !processedContent.error) {
                // Update the file record with extracted content
                this.db.updateFile(fileId, {
                  extracted_content: processedContent.text,
                  metadata: JSON.stringify({
                    ...JSON.parse(fileRecord.metadata || '{}'),
                    processed: true,
                    processingMetadata: processedContent.metadata
                  })
                })
                console.log('[ELECTRON] Vault file content processed successfully:', filename, 'Content length:', processedContent.text.length)
              } else if (processedContent.error) {
                console.warn('[ELECTRON] Vault file processing failed:', filename, processedContent.error)
              } else {
                console.warn('[ELECTRON] Vault file processing returned no text and no error:', filename)
              }
            } catch (error) {
              console.error('[ELECTRON] Error processing vault file content:', filename, error)
            }
          } else {
            console.log('[ELECTRON] File type not supported for processing:', fileType)
          }
        } else {
          console.log('[ELECTRON] processContent not requested for:', filename)
        }

        // Return the file record with database ID
        const finalFileRecord = this.db.getFile(fileId)
        return { success: true, file: finalFileRecord }

      } catch (error: any) {
        console.error('[ELECTRON] files:indexVaultFile error:', error)
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('files:copyFileToUploads', async (event: IpcMainInvokeEvent, sourcePath: string, filename?: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(sourcePath, 'string', 500)) throw new Error('Invalid source path')
      if (filename && !this.validateInput(filename, 'string', 255)) throw new Error('Invalid filename')
      return await this.fileSystem.copyFileToUploads(sourcePath, filename)
    })

    // File attachment operations
    ipcMain.handle('files:addFileAttachment', (event: IpcMainInvokeEvent, messageId: string, fileId: string, attachmentType: 'attachment' | 'reference') => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      if (!this.validateInput(fileId, 'string', 100)) throw new Error('Invalid file ID')
      if (!['attachment', 'reference'].includes(attachmentType)) throw new Error('Invalid attachment type')
      return this.db.addFileAttachment(messageId, fileId, attachmentType)
    })

    ipcMain.handle('files:getFileAttachments', (event: IpcMainInvokeEvent, messageId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      return this.db.getFileAttachments(messageId)
    })

    ipcMain.handle('files:getMessageFiles', (event: IpcMainInvokeEvent, messageId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      return this.db.getMessageFiles(messageId)
    })

    ipcMain.handle('files:removeFileAttachment', (event: IpcMainInvokeEvent, attachmentId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(attachmentId, 'string', 100)) throw new Error('Invalid attachment ID')
      this.db.removeFileAttachment(attachmentId)
    })

    ipcMain.handle('files:saveContentAsFile', async (event: IpcMainInvokeEvent, content: string, filename: string, subfolder?: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(content, 'string', 1000000)) throw new Error('Content too large')
      if (!this.validateInput(filename, 'string', 255)) throw new Error('Invalid filename')
      if (subfolder && !this.validateInput(subfolder, 'string', 100)) throw new Error('Invalid subfolder')
      return await this.fileSystem.saveContentAsFile(content, filename, subfolder)
    })

    ipcMain.handle('files:deleteFile', async (event: IpcMainInvokeEvent, fileId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(fileId, 'string', 100)) throw new Error('Invalid file ID')
      return await this.fileSystem.deleteFile(fileId)
    })

    ipcMain.handle('files:getFileContent', async (event: IpcMainInvokeEvent, filePath: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
      return this.fileSystem.getFileContent(filePath)
    })

    ipcMain.handle('files:fileExists', (event: IpcMainInvokeEvent, filePath: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
      return this.fileSystem.fileExists(filePath)
    })

    ipcMain.handle('files:showOpenDialog', async (event: IpcMainInvokeEvent, options: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      return await dialog.showOpenDialog(this.mainWindow!, options)
    })

    ipcMain.handle('files:showSaveDialog', async (event: IpcMainInvokeEvent, options: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      return await dialog.showSaveDialog(this.mainWindow!, options)
    })

    // Vault IPC handlers
    ipcMain.handle('vault:createDirectory', async (event: IpcMainInvokeEvent, dirPath: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!this.validateInput(dirPath, 'string', 500)) return { success: false, error: 'Invalid directory path' }

      try {
        const fs = await import('fs')
        await fs.promises.mkdir(dirPath, { recursive: true })
        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('vault:writeFile', async (event: IpcMainInvokeEvent, filePath: string, content: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!this.validateInput(filePath, 'string', 500)) return { success: false, error: 'Invalid file path' }
      if (!this.validateInput(content, 'string', 1000000)) return { success: false, error: 'Content too large' }

      try {
        const fs = await import('fs')
        await fs.promises.writeFile(filePath, content, 'utf8')
        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('vault:readDirectory', async (event: IpcMainInvokeEvent, dirPath: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!this.validateInput(dirPath, 'string', 500)) return { success: false, error: 'Invalid directory path' }

      try {
        const fs = await import('fs')
        const path = await import('path')
        const items = await fs.promises.readdir(dirPath, { withFileTypes: true })

        const result = await Promise.all(items.map(async (item) => {
          const itemPath = path.join(dirPath, item.name)
          const stats = await fs.promises.stat(itemPath)

          return {
            name: item.name,
            path: itemPath,
            isDirectory: item.isDirectory(),
            size: item.isFile() ? stats.size : undefined,
            modified: stats.mtime.toISOString()
          }
        }))

        return { success: true, items: result }
      } catch (error: any) {
        return { success: false, error: error.message, items: [] }
      }
    })

    ipcMain.handle('vault:removeDirectory', async (event: IpcMainInvokeEvent, dirPath: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!this.validateInput(dirPath, 'string', 500)) return { success: false, error: 'Invalid directory path' }

      try {
        const fs = await import('fs')
        await fs.promises.rm(dirPath, { recursive: true, force: true })
        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('vault:pathExists', async (event: IpcMainInvokeEvent, targetPath: string) => {
      if (!this.validateSender(event.senderFrame)) return { exists: false, error: 'Unauthorized' }
      if (!this.validateInput(targetPath, 'string', 500)) return { exists: false, error: 'Invalid path' }

      try {
        const fs = await import('fs')
        await fs.promises.access(targetPath)
        return { exists: true }
      } catch (error: any) {
        return { exists: false, error: error.message }
      }
    })

    ipcMain.handle('vault:readFile', async (event: IpcMainInvokeEvent, filePath: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!this.validateInput(filePath, 'string', 500)) return { success: false, error: 'Invalid file path' }

      try {
        const fs = await import('fs')
        const content = await fs.promises.readFile(filePath, 'utf8')
        return { success: true, content }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('vault:removeFile', async (event: IpcMainInvokeEvent, filePath: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!this.validateInput(filePath, 'string', 500)) return { success: false, error: 'Invalid file path' }

      try {
        const fs = await import('fs')
        await fs.promises.unlink(filePath)
        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('vault:scanFolder', async (event: IpcMainInvokeEvent, folderPath: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!this.validateInput(folderPath, 'string', 500)) return { success: false, error: 'Invalid folder path' }

      try {
        const fs = await import('fs')
        const path = await import('path')

        // Check if folder exists
        const stats = await fs.promises.stat(folderPath)
        if (!stats.isDirectory()) {
          return { success: false, error: 'Path is not a directory' }
        }

        // Read directory contents
        const entries = await fs.promises.readdir(folderPath, { withFileTypes: true })

        const files = await Promise.all(entries.map(async (entry) => {
          const fullPath = path.join(folderPath, entry.name)
          let fileStats

          try {
            fileStats = await fs.promises.stat(fullPath)
          } catch (error) {
            // Skip files that can't be accessed
            return null
          }

          return {
            name: entry.name,
            path: fullPath,
            isDirectory: entry.isDirectory(),
            size: entry.isDirectory() ? 0 : fileStats.size,
            lastModified: fileStats.mtime.toISOString()
          }
        }))

        // Filter out null entries (files that couldn't be accessed)
        const validFiles = files.filter(file => file !== null)

        return { success: true, files: validFiles }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('vault:copyFile', async (event: IpcMainInvokeEvent, sourcePath: string, destinationPath: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!this.validateInput(sourcePath, 'string', 500)) return { success: false, error: 'Invalid source path' }
      if (!this.validateInput(destinationPath, 'string', 500)) return { success: false, error: 'Invalid destination path' }

      try {
        const fs = await import('fs')
        const path = await import('path')

        // Ensure destination directory exists
        const destDir = path.dirname(destinationPath)
        await fs.promises.mkdir(destDir, { recursive: true })

        // Copy the file (handles binary files properly)
        await fs.promises.copyFile(sourcePath, destinationPath)

        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    })

    // High-level vault operations
    ipcMain.handle('vault:getVaultRegistry', async (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null

      try {
        const fs = await import('fs')
        const path = await import('path')
        const os = await import('os')

        // First try to get saved vault root path from database
        let vaultRoot = this.db.getSetting('vault-root-path')
        console.log('[ELECTRON] vault:getVaultRegistry - Saved vault root from DB:', vaultRoot)

        // Fall back to default if no saved path
        if (!vaultRoot) {
          vaultRoot = path.join(os.homedir(), 'Documents', 'ChatLo_Vaults')
          console.log('[ELECTRON] vault:getVaultRegistry - Using default vault root:', vaultRoot)
        }

        const registryPath = path.join(vaultRoot, '.chatlo', 'vault-registry.json')
        console.log('[ELECTRON] vault:getVaultRegistry - Looking for registry at:', registryPath)

        if (!await fs.promises.access(registryPath).then(() => true).catch(() => false)) {
          console.log('[ELECTRON] vault:getVaultRegistry - Registry file not found')
          return null
        }

        const content = await fs.promises.readFile(registryPath, 'utf8')
        const registry = JSON.parse(content)
        console.log('[ELECTRON] vault:getVaultRegistry - Registry loaded successfully:', registry)
        return registry
      } catch (error: any) {
        console.error('[ELECTRON] Error reading vault registry:', error)
        return null
      }
    })

    ipcMain.handle('vault:saveVaultRegistry', async (event: IpcMainInvokeEvent, registry: any) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!registry || typeof registry !== 'object') return { success: false, error: 'Invalid registry object' }

      try {
        const fs = await import('fs')
        const path = await import('path')

        const registryPath = path.join(registry.vaultRoot, '.chatlo', 'vault-registry.json')
        const registryDir = path.dirname(registryPath)

        // Ensure directory exists
        await fs.promises.mkdir(registryDir, { recursive: true })

        // Save registry
        await fs.promises.writeFile(registryPath, JSON.stringify(registry, null, 2), 'utf8')
        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('vault:initializeVaultRoot', async (event: IpcMainInvokeEvent, rootPath: string, template: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized', vaults: [] }
      if (!this.validateInput(rootPath, 'string', 500)) return { success: false, error: 'Invalid root path', vaults: [] }
      if (!this.validateInput(template, 'string', 50)) return { success: false, error: 'Invalid template', vaults: [] }

      try {
        const fs = await import('fs')
        const path = await import('path')

        // Create root directory if it doesn't exist
        await fs.promises.mkdir(rootPath, { recursive: true })

        // Create basic vault structure based on template
        const vaults: any[] = []

        if (template === 'default') {
          // Create Personal and Work vaults
          const personalPath = path.join(rootPath, 'personal-vault')
          const workPath = path.join(rootPath, 'work-vault')

          await fs.promises.mkdir(personalPath, { recursive: true })
          await fs.promises.mkdir(workPath, { recursive: true })

          // Create getting-started context in personal vault
          const gettingStartedPath = path.join(personalPath, 'getting-started')
          await this.createContextStructure(gettingStartedPath, 'Your First Context Vault')

          // Create projects context in work vault
          const projectsPath = path.join(workPath, 'projects')
          await this.createContextStructure(projectsPath, 'Projects')

          vaults.push(
            { id: 'personal-vault', name: 'Personal Vault', path: personalPath },
            { id: 'work-vault', name: 'Work Vault', path: workPath }
          )
        } else if (template === 'simple') {
          // Create single vault
          const vaultPath = path.join(rootPath, 'my-vault')
          await fs.promises.mkdir(vaultPath, { recursive: true })

          // Create getting-started context
          const gettingStartedPath = path.join(vaultPath, 'getting-started')
          await this.createContextStructure(gettingStartedPath, 'Getting Started')

          vaults.push({ id: 'my-vault', name: 'My Vault', path: vaultPath })
        }

        return { success: true, vaults }
      } catch (error: any) {
        return { success: false, error: error.message, vaults: [] }
      }
    })

    ipcMain.handle('vault:scanContexts', async (event: IpcMainInvokeEvent, vaultPath: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized', contexts: [] }
      if (!this.validateInput(vaultPath, 'string', 500)) return { success: false, error: 'Invalid vault path', contexts: [] }

      try {
        const fs = await import('fs')
        const path = await import('path')

        const items = await fs.promises.readdir(vaultPath, { withFileTypes: true })
        const contexts: any[] = []

        for (const item of items) {
          if (item.isDirectory() && !item.name.startsWith('.')) {
            const contextPath = path.join(vaultPath, item.name)
            const masterPath = path.join(contextPath, 'master.md')

            // Check if master.md exists
            const masterExists = await fs.promises.access(masterPath).then(() => true).catch(() => false)

            if (masterExists) {
              contexts.push({
                id: item.name,
                name: item.name,
                path: contextPath,
                hasMaster: true
              })
            }
          }
        }

        return { success: true, contexts }
      } catch (error: any) {
        return { success: false, error: error.message, contexts: [] }
      }
    })

    // Artifact IPC handlers
    ipcMain.handle('db:getArtifacts', (event: IpcMainInvokeEvent, messageId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      return this.db.getArtifacts(messageId)
    })

    ipcMain.handle('db:addArtifact', (event: IpcMainInvokeEvent, messageId: string, artifact: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      if (!artifact || typeof artifact !== 'object') throw new Error('Invalid artifact object')
      return this.db.addArtifact(messageId, artifact)
    })

    // Database diagnostics
    ipcMain.handle('db:getDatabaseHealth', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.db.getDatabaseHealth()
    })

    ipcMain.handle('db:createBackup', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.db.createBackup()
    })

    ipcMain.handle('db:updateArtifact', (event: IpcMainInvokeEvent, id: string, updates: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid artifact ID')
      if (!updates || typeof updates !== 'object') throw new Error('Invalid updates object')
      return this.db.updateArtifact(id, updates)
    })

    ipcMain.handle('db:removeArtifact', (event: IpcMainInvokeEvent, id: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid artifact ID')
      return this.db.removeArtifact(id)
    })

    ipcMain.handle('db:getConversationArtifacts', (event: IpcMainInvokeEvent, conversationId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
      return this.db.getConversationArtifacts(conversationId)
    })

    // Auto-updater IPC handlers
    ipcMain.handle('updater:check-for-updates', async (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null

      if (isDev) {
        return { available: false, message: 'Updates not available in development mode' }
      }

      try {
        const result = await autoUpdater.checkForUpdates()
        return { available: result ? result.updateInfo.version !== app.getVersion() : false }
      } catch (error: any) {
        console.error('Error checking for updates:', error)
        return { available: false, error: error.message }
      }
    })

    ipcMain.handle('updater:download-and-install', async (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null

      if (isDev) {
        return { success: false, message: 'Updates not available in development mode' }
      }

      try {
        await autoUpdater.downloadUpdate()
        autoUpdater.quitAndInstall()
        return { success: true }
      } catch (error: any) {
        console.error('Error downloading/installing update:', error)
        return { success: false, error: error.message }
      }
    })
  }

  private setupAutoUpdater(): void {
    if (!isDev) {
      autoUpdater.checkForUpdatesAndNotify()
    }

    // Auto-updater events
    autoUpdater.on('checking-for-update', () => {
      console.log('Checking for update...')
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:checking-for-update')
      }
    })

    autoUpdater.on('update-available', (info) => {
      console.log('Update available:', info.version)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-available', info)
      }
    })

    autoUpdater.on('update-not-available', (info) => {
      console.log('Update not available')
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-not-available')
      }
    })

    autoUpdater.on('error', (err) => {
      console.error('Auto-updater error:', err)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:error', err.message)
      }
    })

    autoUpdater.on('download-progress', (progressObj) => {
      console.log(`Download progress: ${progressObj.percent}%`)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:download-progress', progressObj)
      }
    })

    autoUpdater.on('update-downloaded', (info) => {
      console.log('Update downloaded:', info.version)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-downloaded', info)
      }
    })
  }

  // Register core API endpoints
  private registerCoreAPIs(): void {
    // Database APIs
    this.apiRegistry.registerCategory('db')
    this.apiRegistry.registerEndpoint('db', 'getConversations', 
      () => this.db.getConversations(),
      { description: 'Get all conversations' }
    )
    
    // File APIs
    this.apiRegistry.registerCategory('files')
    this.apiRegistry.registerEndpoint('files', 'processFileContent',
      async (fileId: string) => {
        return this.fileSystem.processFileContent(fileId)
      },
      { 
        validator: (fileId: string) => {
          if (!fileId || typeof fileId !== 'string') {
            throw new Error('Invalid file ID')
          }
        },
        description: 'Process file content using plugins'
      }
    )
    
    // Plugin management APIs
    this.apiRegistry.registerCategory('plugins')
    this.apiRegistry.registerEndpoint('plugins', 'getAll',
      () => this.pluginManager.getAllPluginsInfo(),
      { description: 'Get all plugin information' }
    )
    
    this.apiRegistry.registerEndpoint('plugins', 'enable',
      (pluginId: string, enabled: boolean) => {
        this.pluginManager.setPluginEnabled(pluginId, enabled)
        return { success: true }
      },
      { 
        validator: (pluginId: string, enabled: boolean) => {
          if (!pluginId || typeof enabled !== 'boolean') {
            throw new Error('Invalid parameters')
          }
        },
        description: 'Enable or disable a plugin'
      }
    )
    
    this.apiRegistry.registerEndpoint('plugins', 'discover',
      () => this.pluginManager.discoverPlugins(),
      { description: 'Discover available plugins' }
    )
  }

  // Initialize plugins
  private async initializePlugins(): Promise<void> {
    try {
      // Discover plugins
      const manifests = await this.pluginManager.discoverPlugins()
      console.log(`Discovered ${manifests.length} plugins`)
      
      // Load core plugins first
      const corePlugins = manifests.filter(m => m.id.startsWith('core-'))
      for (const manifest of corePlugins) {
        try {
          await this.pluginManager.loadPlugin(manifest)
        } catch (error) {
          console.error(`Failed to load core plugin ${manifest.id}:`, error)
        }
      }
      
      // Load optional plugins
      const optionalPlugins = manifests.filter(m => !m.id.startsWith('core-'))
      for (const manifest of optionalPlugins) {
        try {
          await this.pluginManager.loadPlugin(manifest)
        } catch (error) {
          console.warn(`Failed to load optional plugin ${manifest.id}:`, error)
        }
      }
      
    } catch (error) {
      console.error('Error initializing plugins:', error)
    }
  }

  public async init(): Promise<void> {
    await app.whenReady()

    // Check if vault system is configured - required for new architecture
    try {
      const vaultRootPath = this.db.getSetting('vault-root-path')
      console.log('[ELECTRON] Checking for vault system, vault root path:', vaultRootPath)

      if (vaultRootPath) {
        console.log('[ELECTRON] Vault system detected and ready')
      } else {
        console.log('[ELECTRON] No vault system configured - user will need to set up context vault on first run')
      }
    } catch (error) {
      console.error('Error checking vault system:', error)
    }

    this.setupIPC()
    await this.initializePlugins()
    this.setupAutoUpdater()
    this.createWindow()

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow()
      }
    })

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit()
      }
    })
  }
}

const application = new App()
application.init().catch(console.error)

ipcMain.on('window-minimize', () => {
  if (application.mainWindow) application.mainWindow.minimize();
});
ipcMain.on('window-maximize', () => {
  if (application.mainWindow) {
    if (application.mainWindow.isMaximized()) {
      application.mainWindow.unmaximize();
    } else {
      application.mainWindow.maximize();
    }
  }
});
ipcMain.on('window-close', () => {
  if (application.mainWindow) application.mainWindow.close();
});
